import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getDefaultProfile, UserProfile as ImportedUserProfile } from '../constants/UserData';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatabaseIntegrationService from '../services/DatabaseIntegrationService';
import NotificationService from '../services/NotificationService';

// Use the interface from UserData.ts
export type UserProfile = ImportedUserProfile;

// Meal interface for detailed tracking
export interface MealEntry {
  id: string;
  name: string;
  time: string;
  calories: number;
  protein?: number; // Add protein tracking
  carbs?: number;   // Add carbs tracking
  fat?: number;     // Add fat tracking
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: number;
}

// Daily tracking data interface
export interface DailyData {
  date: string;
  caloriesConsumed: number;
  proteinConsumed: number;
  waterConsumed: number;
  stepsCompleted: number;
  workoutsCompleted: number;
  mealsLogged: string[];
  recentMeals: MealEntry[];
  weight?: number;
  mood?: string;
  energyLevel?: number; // 1-10
  sleepHours?: number;
}

// Context interface
interface ProfileContextType {
  profile: UserProfile;
  dailyData: DailyData;
  isProfileLoaded: boolean;
  updateProfile: (field: keyof UserProfile, value: any) => Promise<void>;
  updateProfileBatch: (updates: Partial<UserProfile>) => Promise<void>;
  updateDailyData: (field: keyof DailyData, value: any) => void;
  refreshProfile: () => Promise<void>;
  calculateBMI: () => number;
  calculateCaloriesRemaining: () => number;
  calculateProteinRemaining: () => number;
  calculateWaterRemaining: () => number;
  calculateStepsRemaining: () => number;
  getProgressPercentage: (type: 'calories' | 'protein' | 'water' | 'steps') => number;
  addMealToLog: (mealName: string) => void;
  addRecentMeal: (meal: Omit<MealEntry, 'id' | 'timestamp'>) => Promise<void>;
  getRecentMeals: () => MealEntry[];
  clearRecentMeals: () => Promise<void>;
  incrementWater: () => void;
  updateWeight: (newWeight: number) => void;
  getHealthScore: () => number;
  getWeeklyAverage: (metric: string) => number;
  recalculateNutrition: () => void;
}

// Create context
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Provider component
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<UserProfile>(getDefaultProfile());
  const [isProfileLoaded, setIsProfileLoaded] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dailyData, setDailyData] = useState<DailyData>({
    date: new Date().toISOString().split('T')[0],
    caloriesConsumed: 0, // Start with zero - real data only
    proteinConsumed: 0,
    waterConsumed: 0,
    stepsCompleted: 0,
    workoutsCompleted: 0,
    mealsLogged: [], // Empty array - no mock meals
    recentMeals: [], // Empty array - will be loaded from database
    weight: 0, // Will be set from profile
    mood: '',
    energyLevel: 0,
    sleepHours: 0,
  });

  const dbService = DatabaseIntegrationService;
  const notificationService = NotificationService;

  // Initialize profile from storage or create new one
  useEffect(() => {
    const initProfile = async () => {
      try {
        const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
        if (onboardingComplete === 'true') {
          // User has completed onboarding, load their profile
          await initializeProfile();
        } else {
          // New user, use default profile and mark as loaded immediately
          setProfile(getDefaultProfile());
          setIsProfileLoaded(true);
        }
      } catch (error) {
        // Fallback: use default profile and mark as loaded
        setProfile(getDefaultProfile());
        setIsProfileLoaded(true);
      }
    };
    initProfile();
  }, []);

  const initializeProfile = async (forceRefresh = false) => {
    // Prevent multiple simultaneous initializations with proper mutex
    if (!forceRefresh && (isProfileLoaded || isInitializing)) {
      console.log('⚠️ Profile already loaded or initializing, skipping...');
      return;
    }

    setIsInitializing(true);
    try {
      console.log('🔄 Initializing profile...');

      // Try AsyncStorage first (more reliable), then database
      let userProfile = null;

      // Load from AsyncStorage first with validation
      const savedProfile = await AsyncStorage.getItem('userProfile');
      console.log('💾 AsyncStorage profile result:', savedProfile ? 'Found' : 'Not found');

      if (savedProfile) {
        try {
          const parsedProfile = JSON.parse(savedProfile);

          // Validate parsed profile data
          if (parsedProfile && typeof parsedProfile === 'object' && parsedProfile.name) {
            // Additional validation for critical fields
            if (validateProfileData(parsedProfile)) {
              userProfile = parsedProfile;
              console.log('✅ Successfully loaded and validated profile from AsyncStorage:', userProfile.name || 'No name');
            } else {
              console.error('❌ Invalid profile data in AsyncStorage, will use default');
              // Clear corrupted data
              await AsyncStorage.removeItem('userProfile');
            }
          } else {
            console.error('❌ Malformed profile data in AsyncStorage');
            await AsyncStorage.removeItem('userProfile');
          }
        } catch (parseError) {
          console.error('❌ Error parsing saved profile from AsyncStorage:', parseError);
          // Clear corrupted data
          await AsyncStorage.removeItem('userProfile');
        }
      }

      // If no AsyncStorage profile, try database
      if (!userProfile) {
        try {
          userProfile = await dbService.loadProfileFromDatabase();
          console.log('📊 Database profile result:', userProfile ? 'Found' : 'Not found');

          if (userProfile) {
            // Save to AsyncStorage for faster future access
            await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
            console.log('✅ Database profile cached to AsyncStorage');
          }
        } catch (dbError) {
          console.error('❌ Error loading from database:', dbError);
        }
      }

      // If still no profile, use default
      if (!userProfile) {
        userProfile = getDefaultProfile();
        console.log('🆕 Using default profile for new user');

        // Save default profile to AsyncStorage
        await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
      }

      console.log('🔧 Setting profile state with:', userProfile.name || 'No name');
      setProfile(userProfile);

      // Try to load additional data from database, but don't let it block profile loading
      try {
        // Load recent meals from database
        const recentMeals = await dbService.getRecentMealsFromDatabase(10);

        // Load today's health data from database
        const todaysHealth = await dbService.loadTodaysHealthData();

        // Calculate today's nutrition from database
        const todaysNutrition = await dbService.calculateTodaysNutrition();

        // Initialize daily data with database values
        setDailyData(prev => ({
          ...prev,
          weight: todaysHealth?.weight || userProfile.weight,
          recentMeals,
          caloriesConsumed: todaysNutrition.calories,
          proteinConsumed: todaysNutrition.protein,
          mealsLogged: recentMeals.slice(0, todaysNutrition.mealCount).map(meal => meal.name),
          waterConsumed: todaysHealth?.waterIntake || 0,
          stepsCompleted: todaysHealth?.steps || 0,
          sleepHours: todaysHealth?.sleepHours || 0,
          energyLevel: todaysHealth?.energyLevel || 0,
          mood: todaysHealth?.mood || ''
        }));

        // Recalculate nutrition from today's meals after loading (using proper async)
        const today = new Date().toISOString().split('T')[0];
        const todaysMeals = recentMeals.filter((meal: MealEntry) => {
          const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
          return mealDate === today;
        });

        const totalCalories = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.calories || 0), 0);
        const totalProtein = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.protein || 0), 0);

        if (totalCalories > 0 || totalProtein > 0) {
          setDailyData(prev => ({
            ...prev,
            caloriesConsumed: totalCalories,
            proteinConsumed: totalProtein,
            mealsLogged: todaysMeals.map((meal: MealEntry) => meal.name)
          }));
        }

        // Schedule daily achievement notifications
        await notificationService.scheduleDailyAchievementNotification();
      } catch (dbError) {
        console.error('❌ Database operations failed, but profile still loaded:', dbError);
      }

      // ALWAYS mark profile as loaded, regardless of database operations
      setIsProfileLoaded(true);

    } catch (error) {
      console.error('❌ Error initializing profile:', error);
      // Fallback to default profile
      const defaultProfile = getDefaultProfile();
      setProfile(defaultProfile);
      setIsProfileLoaded(true); // Still mark as loaded even with default profile
    } finally {
      setIsInitializing(false);
    }
  };

  // NOTE: App state handling moved to App.tsx to prevent multiple listeners
  // This prevents refresh loops and ensures centralized state management

  // Validate profile data before updates
  const validateProfileData = (profileData: Partial<UserProfile>): boolean => {
    try {
      // Basic validation rules
      if (profileData.age && (profileData.age < 0 || profileData.age > 150)) return false;
      if (profileData.height && (profileData.height < 50 || profileData.height > 300)) return false;
      if (profileData.weight && (profileData.weight < 20 || profileData.weight > 500)) return false;
      if (profileData.caloriesGoal && (profileData.caloriesGoal < 800 || profileData.caloriesGoal > 5000)) return false;
      if (profileData.proteinGoal && (profileData.proteinGoal < 20 || profileData.proteinGoal > 400)) return false;
      return true;
    } catch (error) {
      console.error('❌ Profile validation error:', error);
      return false;
    }
  };

  // Update profile function with optimistic updates and rollback
  const updateProfile = async (field: keyof UserProfile, value: any) => {
    // Validate the update
    const testUpdate = { [field]: value };
    if (!validateProfileData(testUpdate)) {
      console.error('❌ Invalid profile data:', field, value);
      return;
    }

    // Store original profile for rollback
    const originalProfile = { ...profile };

    const updatedProfile = {
      ...profile,
      [field]: value
    };

    // Recalculate BMI if height or weight changed
    if (field === 'height' || field === 'weight') {
      const height = updatedProfile.height || profile.height;
      const weight = updatedProfile.weight || profile.weight;
      if (height && weight) {
        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);
        updatedProfile.bmi = Math.round(bmi * 10) / 10;
      }
    }

    // Optimistic update - update UI immediately
    setProfile(updatedProfile);

    // Sync to database and AsyncStorage with proper rollback
    try {
      // Always save to AsyncStorage first (more reliable)
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('✅ Profile saved to AsyncStorage:', field, value);

      // Then try to sync to database
      try {
        await dbService.syncProfileToDatabase(updatedProfile);
        console.log('✅ Profile synced to database:', field, value);
      } catch (dbError) {
        console.error('⚠️ Database sync failed, but AsyncStorage saved:', dbError);
        // Database failure is not critical since AsyncStorage succeeded
      }
    } catch (error) {
      console.error('❌ Critical error saving profile:', error);
      // Rollback to original profile if save failed
      setProfile(originalProfile);
      throw error; // Re-throw to let caller know save failed
    }
  };

  // Batch update profile function for onboarding
  const updateProfileBatch = async (updates: Partial<UserProfile>) => {
    const updatedProfile = {
      ...profile,
      ...updates
    };

    // Recalculate BMI if height or weight changed
    if (updates.height || updates.weight) {
      const height = updates.height || profile.height;
      const weight = updates.weight || profile.weight;
      if (height && weight) {
        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);
        updatedProfile.bmi = Math.round(bmi * 10) / 10;
      }
    }

    setProfile(updatedProfile);

    // Save to AsyncStorage and database
    try {
      // Always save to AsyncStorage first (more reliable)
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('✅ Profile batch saved to AsyncStorage:', Object.keys(updates));
      console.log('📋 Saved profile data:', JSON.stringify(updatedProfile, null, 2));

      // Verify the save worked
      const verification = await AsyncStorage.getItem('userProfile');
      if (verification) {
        const parsed = JSON.parse(verification);
        console.log('✅ AsyncStorage verification successful:', parsed.name || 'No name');
      } else {
        console.error('❌ AsyncStorage verification failed - no data found');
      }

      // Then try to sync to database
      try {
        await dbService.syncProfileToDatabase(updatedProfile);
        console.log('✅ Profile batch synced to database:', Object.keys(updates));
      } catch (dbError) {
        console.error('⚠️ Database batch sync failed, but AsyncStorage saved:', dbError);
      }
    } catch (error) {
      console.error('❌ Critical error saving profile batch:', error);
      // Try to revert the state change if save failed
      setProfile(profile);
    }
  };

  // Update daily data function
  const updateDailyData = (field: keyof DailyData, value: any) => {
    setDailyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Refresh profile data - useful after onboarding completion
  const refreshProfile = async () => {
    if (isRefreshing || isInitializing) {
      console.log('⚠️ Profile refresh already in progress, waiting...');
      // Wait for current operation to complete
      while (isRefreshing || isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return;
    }

    setIsRefreshing(true);
    try {
      console.log('🔄 Refreshing profile data...');

      // Force re-check of onboarding status and re-initialize
      const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
      console.log('🔍 Onboarding status during refresh:', onboardingComplete);

      if (onboardingComplete === 'true') {
        // During onboarding completion, force refresh the profile
        console.log('🔄 Force initializing profile after onboarding completion...');
        await initializeProfile(true); // Force refresh
        console.log('✅ Profile refresh completed successfully');
      } else {
        // Still not complete, use default profile
        setProfile(getDefaultProfile());
        setIsProfileLoaded(true);
        console.log('✅ Using default profile (onboarding not complete)');
      }
    } catch (error) {
      console.error('❌ Profile refresh failed:', error);
      // Ensure profile is marked as loaded even if refresh fails
      setProfile(getDefaultProfile());
      setIsProfileLoaded(true);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Calculate BMI
  const calculateBMI = (): number => {
    const heightInMeters = profile.height / 100;
    return Math.round((profile.weight / (heightInMeters * heightInMeters)) * 10) / 10;
  };

  // Calculate remaining values
  const calculateCaloriesRemaining = (): number => {
    return Math.max(0, profile.caloriesGoal - dailyData.caloriesConsumed);
  };

  const calculateProteinRemaining = (): number => {
    return Math.max(0, profile.proteinGoal - dailyData.proteinConsumed);
  };

  const calculateWaterRemaining = (): number => {
    return Math.max(0, profile.waterGoal - dailyData.waterConsumed);
  };

  const calculateStepsRemaining = (): number => {
    return Math.max(0, profile.stepsGoal - dailyData.stepsCompleted);
  };

  // Calculate progress percentages
  const getProgressPercentage = (type: 'calories' | 'protein' | 'water' | 'steps'): number => {
    switch (type) {
      case 'calories':
        return Math.min(100, (dailyData.caloriesConsumed / profile.caloriesGoal) * 100);
      case 'protein':
        return Math.min(100, (dailyData.proteinConsumed / profile.proteinGoal) * 100);
      case 'water':
        return Math.min(100, (dailyData.waterConsumed / profile.waterGoal) * 100);
      case 'steps':
        return Math.min(100, (dailyData.stepsCompleted / profile.stepsGoal) * 100);
      default:
        return 0;
    }
  };

  // Add meal to log
  const addMealToLog = (mealName: string) => {
    setDailyData(prev => ({
      ...prev,
      mealsLogged: [...prev.mealsLogged, mealName]
    }));
  };

  // Add recent meal with database sync and nutrition calculation
  const addRecentMeal = async (meal: Omit<MealEntry, 'id' | 'timestamp'>) => {
    try {
      const newMeal: MealEntry = {
        ...meal,
        id: Date.now().toString(),
        timestamp: Date.now()
      };

      // Sync to database first
      await dbService.syncMealToDatabase(newMeal);

      // Get updated meals from database to ensure consistency
      const updatedMeals = await dbService.getRecentMealsFromDatabase(10);

      // Calculate today's nutrition from database
      const todaysNutrition = await dbService.calculateTodaysNutrition();

      // Update state with database values
      setDailyData(prev => ({
        ...prev,
        recentMeals: updatedMeals,
        caloriesConsumed: todaysNutrition.calories,
        proteinConsumed: todaysNutrition.protein,
        mealsLogged: [...prev.mealsLogged, meal.name]
      }));

      // Keep AsyncStorage for backward compatibility
      await AsyncStorage.setItem('recentMeals', JSON.stringify(updatedMeals));

      // Check for achievements and send notifications
      await checkAndSendAchievementNotifications(todaysNutrition);

      console.log('✅ Meal added and synced to database:', meal.name);
    } catch (error) {
      console.error('❌ Error adding recent meal:', error);
    }
  };

  // Get recent meals
  const getRecentMeals = (): MealEntry[] => {
    return dailyData.recentMeals.slice(0, 5); // Return only the 5 most recent
  };

  // Clear recent meals
  const clearRecentMeals = async () => {
    try {
      setDailyData(prev => ({
        ...prev,
        recentMeals: []
      }));
      await AsyncStorage.removeItem('recentMeals');
    } catch (error) {
      console.error('Error clearing recent meals:', error);
    }
  };

  // Increment water
  const incrementWater = () => {
    setDailyData(prev => ({
      ...prev,
      waterConsumed: Math.min(prev.waterConsumed + 1, profile.waterGoal + 2)
    }));
  };

  // Update weight
  const updateWeight = (newWeight: number) => {
    updateProfile('weight', newWeight);
    setDailyData(prev => ({
      ...prev,
      weight: newWeight
    }));
  };

  // Calculate health score
  const getHealthScore = (): number => {
    const caloriesScore = Math.min(100, getProgressPercentage('calories'));
    const proteinScore = Math.min(100, getProgressPercentage('protein'));
    const waterScore = Math.min(100, getProgressPercentage('water'));
    const stepsScore = Math.min(100, getProgressPercentage('steps'));
    
    return Math.round((caloriesScore + proteinScore + waterScore + stepsScore) / 4);
  };

  // Check and send achievement notifications
  const checkAndSendAchievementNotifications = async (nutrition: any) => {
    try {
      // Check calorie goal achievement
      if (nutrition.calories >= profile.caloriesGoal && nutrition.calories < profile.caloriesGoal + 100) {
        await notificationService.sendAchievementNotification({
          title: '🎯 Calorie Goal Achieved!',
          message: `Great job! You've reached your daily calorie goal of ${profile.caloriesGoal} calories.`,
          achievementType: 'daily_goals',
          data: { type: 'calories', value: nutrition.calories, goal: profile.caloriesGoal }
        });
      }

      // Check protein goal achievement
      if (nutrition.protein >= profile.proteinGoal && nutrition.protein < profile.proteinGoal + 10) {
        await notificationService.sendAchievementNotification({
          title: '💪 Protein Target Hit!',
          message: `Excellent! You've met your protein goal of ${profile.proteinGoal}g today.`,
          achievementType: 'daily_goals',
          data: { type: 'protein', value: nutrition.protein, goal: profile.proteinGoal }
        });
      }

      // Check meal logging streak
      if (nutrition.mealCount >= 3) {
        await notificationService.sendAchievementNotification({
          title: '🍽️ Meal Logging Champion!',
          message: `You've logged ${nutrition.mealCount} meals today. Keep up the great tracking!`,
          achievementType: 'streak',
          data: { type: 'meals', count: nutrition.mealCount }
        });
      }
    } catch (error) {
      console.error('❌ Error checking achievements:', error);
    }
  };

  // Recalculate nutrition from all logged meals for today
  const recalculateNutrition = () => {
    const today = new Date().toISOString().split('T')[0];
    const todaysMeals = dailyData.recentMeals.filter(meal => {
      const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
      return mealDate === today;
    });

    const totalCalories = todaysMeals.reduce((sum, meal) => sum + (meal.calories || 0), 0);
    const totalProtein = todaysMeals.reduce((sum, meal) => sum + (meal.protein || 0), 0);

    setDailyData(prev => ({
      ...prev,
      caloriesConsumed: totalCalories,
      proteinConsumed: totalProtein,
      mealsLogged: todaysMeals.map(meal => meal.name)
    }));
  };

  // Get weekly average (real implementation)
  const getWeeklyAverage = (metric: string): number => {
    // In a real app, this would calculate from stored daily data
    // For now, return current day's data if available
    switch (metric) {
      case 'calories':
        return dailyData.caloriesConsumed > 0 ? dailyData.caloriesConsumed : 0;
      case 'protein':
        return dailyData.proteinConsumed > 0 ? dailyData.proteinConsumed : 0;
      case 'water':
        return dailyData.waterConsumed > 0 ? dailyData.waterConsumed : 0;
      case 'steps':
        return dailyData.stepsCompleted > 0 ? dailyData.stepsCompleted : 0;
      case 'weight':
        return (dailyData.weight && dailyData.weight > 0) ? dailyData.weight : (profile.weight || 0);
      case 'sleep':
        return (dailyData.sleepHours && dailyData.sleepHours > 0) ? dailyData.sleepHours : 0;
      case 'energy':
        return (dailyData.energyLevel && dailyData.energyLevel > 0) ? dailyData.energyLevel : 0;
      default:
        return 0;
    }
  };

  const contextValue: ProfileContextType = {
    profile,
    dailyData,
    isProfileLoaded,
    updateProfile,
    updateProfileBatch,
    updateDailyData,
    refreshProfile,
    calculateBMI,
    calculateCaloriesRemaining,
    calculateProteinRemaining,
    calculateWaterRemaining,
    calculateStepsRemaining,
    getProgressPercentage,
    addMealToLog,
    addRecentMeal,
    getRecentMeals,
    clearRecentMeals,
    incrementWater,
    updateWeight,
    getHealthScore,
    getWeeklyAverage,
    recalculateNutrition,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
};

// Custom hook to use profile context
export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export default ProfileContext;
