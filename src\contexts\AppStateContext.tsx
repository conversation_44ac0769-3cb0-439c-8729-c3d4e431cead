import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AppStateContextType {
  isOnboardingComplete: boolean | null;
  completeOnboardingFlow: () => Promise<void>;
  checkOnboardingStatus: () => Promise<void>;
}

const AppStateContext = createContext<AppStateContextType | undefined>(undefined);

export const AppStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOnboardingComplete, setIsOnboardingComplete] = useState<boolean | null>(null);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  // NOTE: App state handling moved to App.tsx to prevent multiple listeners
  // This prevents refresh loops and ensures centralized state management

  const checkOnboardingStatus = async () => {
    try {
      const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
      setIsOnboardingComplete(onboardingComplete === 'true');
    } catch (error) {
      setIsOnboardingComplete(false);
    }
  };

  const completeOnboardingFlow = async () => {
    try {
      // Ensure AsyncStorage is updated
      await AsyncStorage.setItem('onboardingComplete', 'true');
      console.log('✅ AppStateContext: onboardingComplete saved to AsyncStorage');

      // Update state
      setIsOnboardingComplete(true);
      console.log('✅ AppStateContext: isOnboardingComplete state updated to true');

      // Force a re-check to ensure consistency
      await new Promise(resolve => setTimeout(resolve, 100));
      await checkOnboardingStatus();
      console.log('✅ AppStateContext: Forced re-check completed');
    } catch (error) {
      console.error('❌ AppStateContext: Error completing onboarding flow:', error);
      // Still update state even if AsyncStorage fails
      setIsOnboardingComplete(true);
    }
  };

  const contextValue: AppStateContextType = {
    isOnboardingComplete,
    completeOnboardingFlow,
    checkOnboardingStatus,
  };

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  );
};

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};
